# **SaaS Image Retoucher via IA avec tokens**

### **1️⃣ Cas d’utilisation simplifié**

**Acteurs :**

* **Utilisateur** : upload, retouche IA, export, achat de tokens.
* **Admin** : supervise utilisateurs, gère tokens si besoin.
* **Système de paiement** : valide achat de tokens.
* **Google AI Studio** : effectue les retouches IA.

**Cas d’utilisation :**

* Upload image
* Retouche IA (consomme 1 token par retouche)
* Export image
* Acheter tokens
* Admin : voir utilisateurs, vérifier consommation de tokens

---

### **2️⃣ Diagramme de classes simplifié**

**Classes :**

1. **Utilisateur**

   * Attributs : `idUtilisateur`, `nom`, `email`, `tokensDisponibles`
   * Méthodes : `uploadImage()`, `retoucherImage()`, `exporterImage()`, `acheterTokens()`

2. **Image**

   * Attributs : `idImage`, `url`, `dateUpload`, `etatRetouche`
   * Méthodes : `ajouterRetouche()`, `exporter()`
   * Relation : un utilisateur peut avoir **plusieurs images**

3. **RetoucheIA**

   * Attributs : `idRetouche`, `typeRetouche`, `date`, `status`
   * Méthodes : `appliquerRetouche()`
   * Relation : une image peut avoir **plusieurs retouches**

4. **Paiement**

   * Attributs : `idPaiement`, `montant`, `date`, `typePaiement`
   * Méthodes : `validerPaiement()`
   * Relation : un utilisateur peut effectuer **plusieurs paiements**

5. **Admin**

   * Attributs : `idAdmin`, `nom`, `email`
   * Méthodes : `gererUtilisateur()`
   * Relation : supervise tous les utilisateurs

6. **GoogleAIStudio**

   * Méthodes : `retoucherImage()`
   * Relation : utilisé par RetoucheIA

---

### **3️⃣ Stack technologique recommandé (version tokens uniquement)**

* **Backend** : Laravel/PHP (facile pour gérer tokens, paiement, auth)
* **Frontend** : Blade (intégration facile avec Laravel), TailwindCSS pour UI
* **Base de données** :  MySQL
* **Stockage images** : Google Cloud Storage ou AWS S3
* **IA** : Google AI Studio via API
* **Paiement** : Flutterwave ou PayDunya pour l’Afrique de l’Ouest

